<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
    >
      <el-form-item label="工单类型" prop="taskType">
        <el-select
          v-model="queryParams.taskType"
          placeholder="请选择工单类型"
          style="width: 200px"
          @keyup.enter="handleQuery"
        >
          <el-option
            v-for="item in task_type"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="请选择状态"
          style="width: 200px"
          @keyup.enter="handleQuery"
        >
          <el-option
            v-for="item in status"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['task:task:add']"
          >新增</el-button
        >
      </el-col>

      <right-toolbar
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>
    <el-table
      v-loading="loading"
      :data="logList"
      @selection-change="handleSelectionChange"
    >
      <!-- <el-table-column type="selection" width="55" align="center" /> -->
      <el-table-column label="序号" align="center" type="index" width="100" />
      <el-table-column label="车场" align="center" prop="parkName" />
      <el-table-column label="创建人" align="center" prop="createBy" />

      <el-table-column label="手机号" align="center" prop="phone" />
      <el-table-column label="工单类型" align="center" prop="taskType">
        <template #default="scope">
          <dict-tag :value="scope.row.taskType" :options="task_type" />
        </template>
      </el-table-column>

      <el-table-column label="施工人员" align="center" prop="executor" />
      <el-table-column label="任务概况" align="center" prop="remark" />
      <el-table-column label="创建时间" align="center" prop="createdTime">
        <template #default="scope">
          {{ parseTime(scope.row.createdTime) }}
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status">
        <template #default="scope">
          <dict-tag :value="scope.row.status" :options="status" />
        </template>
      </el-table-column>
      <el-table-column label="完成时间" align="center" prop="finishTime">
        <template #default="scope">
          {{ parseTime(scope.row.finishTime) }}
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改公告对话框 -->
    <el-dialog :title="title" v-model="open" width="580px" append-to-body>
      <el-form ref="tagRef" :model="form" :rules="rules" label-width="80px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="车场" prop="parkId">
              <el-select
                v-model="form.parkId"
                clearable
                reserve-keyword
                remote
                filterable
                :remote-method="parkList"
                @change="parkChange"
              >
                <el-option
                  v-for="(dict, i) in parkList"
                  :key="i"
                  :label="dict.parkName"
                  :value="dict.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="工单类型" prop="taskType">
              <el-select
                v-model="form.taskType"
                placeholder="请选择工单类型"
                style="width: 200px"
                @keyup.enter="handleQuery"
              >
                <el-option
                  v-for="item in task_type"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="施工人员" prop="executor">
              <el-select
                v-model="form.executor"
                placeholder="请选择"
                clearable
             
              >
                <el-option
                  v-for="item in userData"
                  :key="item.phonenumber"
                  :label="item.nickName"
                  :value="item.phonenumber"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="任务概况" prop="remark">
              <el-input
                v-model="form.remark"
                placeholder="请输入任务概况"
                clearable
                type="textarea"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Notice">
import { listTask, addTask } from "@/api/task/task";

import { listPark,listParkAdmin } from "@/api/system/park";
// import { listUser } from "@/api/system/user";
const { proxy } = getCurrentInstance();

const logList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const parkList = ref([]);
const userData = ref([]); // 微信用户列表

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    taskType: undefined,
    status: undefined,
  },
  rules: {
    parkId: [{ required: true, message: "车场不能为空", trigger: "change" }],
    taskType: [
      { required: true, message: "工单类型不能为空", trigger: "change" },
    ],
    executor: [
      { required: true, message: "施工人员不能为空", trigger: "change" },
    ],
    remark: [{ required: true, message: "任务概况不能为空", trigger: "blur" }],
  },
});

const { queryParams, form, rules } = toRefs(data);

/** 查询公告列表 */
function getList() {
  loading.value = true;
  listTask(queryParams.value).then((response) => {
    logList.value = response.rows;
    total.value = parseInt(response.total);
    loading.value = false;
  });
}

/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}

/** 表单重置 */
function reset() {
  form.value = {
    noticeId: undefined,
    noticeTitle: undefined,
    noticeType: undefined,
    noticeContent: undefined,
    status: "0",
  };
  proxy.resetForm("tagRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.noticeId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加工单";
}

/**修改按钮操作 */
function handleUpdate(row) {
  reset();
  const tagId = row.id || ids.value;
  getTag(tagId).then((response) => {
    form.value = response.data;
    open.value = true;
    title.value = "修改工单";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["tagRef"].validate((valid) => {
    if (valid) {
      addTask(form.value).then((response) => {
        proxy.$modal.msgSuccess("新增成功");
        open.value = false;
        getList();
      });
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const tagIds = row.id || ids.value;
  proxy.$modal
    .confirm('是否确认删除工单编号为"' + tagIds + '"的数据项？')
    .then(function () {
      return delTag(tagIds);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}
// 查询所有的车场列表
function queryParkList() {
  listPark().then((response) => {
    parkList.value = response.rows;
  });
}
// 根据手机号查询微信用户
function queryWxUserByPhone(e) {
  listParkAdmin({
    phone: e,
  }).then((response) => {
    userData.value = response.data;
  });
}
// 车场的切换
function parkChange(e) {
  let item = parkList.value.find((item) => {
    return e == item.id;
  });
  form.value.parkAddress = item.parkAddress;
}
getList();
queryParkList();
queryWxUserByPhone();
// 静态数据
//操作类型 value = "工单类型", allowableValues = "1, 2, 3"
const task_type = [
  { label: "维修", value: "1" },
  { label: "安装", value: "2" },
  { label: "其他", value: "3" },
];
//设备状态 value = "设备状态", allowableValues = "0, 1, 2"
const status = [
  { label: "未完成", value: "0" },
  { label: "已完成", value: "1" },
];
</script>
